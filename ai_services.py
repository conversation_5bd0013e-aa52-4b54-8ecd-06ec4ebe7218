import google.generativeai as genai
import openai
import os
from typing import Dict, Any, Optional
import pytesseract
from PIL import Image
import io
import requests

# Set the Tesseract executable path explicitly for reliability
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

class AIService:
    def __init__(self, mistral_model="mistral-small"):
        self.gemini_model = None
        self.gemini_chat = None
        self.mistral_client = None
        self.mistral_model = mistral_model
        self.system_prompt = self.load_system_prompt()

    def load_system_prompt(self):
        try:
            with open(os.path.join(os.path.dirname(__file__), 'system_prompt.txt'), 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception:
            return "You are an AI assistant designed to help users with a wide range of tasks. "
    
    def initialize_gemini(self, api_key: str) -> bool:
        """Initialize Gemini API"""
        try:
            genai.configure(api_key=api_key)
            self.gemini_model = genai.GenerativeModel('gemini-2.0-flash')
            self.gemini_chat = self.gemini_model.start_chat(history=[])
            return True
        except Exception as e:
            print(f"Error initializing Gemini: {e}")
            return False

    def initialize_openai(self, api_key: str) -> bool:
        """Initialize OpenAI API"""
        try:
            openai.api_key = api_key
            return True
        except Exception as e:
            print(f"Error initializing OpenAI: {e}")
            return False
        
    def initialize_mistral(self, api_key: str) -> bool:
        """Initialize Mistral API"""
        try:
            from mistralai import Mistral
            # Sanitize the API key to avoid illegal header errors
            api_key = api_key.strip().splitlines()[0]
            self.mistral_client = Mistral(api_key=api_key)
            # Test the connection with a simple request
            test_response = self.mistral_client.chat.complete(
                model=self.mistral_model,
                messages=[{"role": "user", "content": "test"}]
            )
            # If the test_response is a 401 error, raise a clear error
            if hasattr(test_response, 'status_code') and test_response.status_code == 401:
                raise Exception("Unauthorized: Your Mistral API key is invalid or expired.")
            return True if test_response else False
        except Exception as e:
            print(f"Error initializing Mistral: {e}")
            return False

    def initialize_deepseek(self, api_key: str) -> bool:
        """Initialize DeepSeek (OpenRouter) API"""
        try:
            self.openrouter_api_key = api_key.strip()
            self.deepseek_model = "deepseek/deepseek-r1-0528:free"
            # Optionally, test the connection with a simple request
            headers = {
                "Authorization": f"Bearer {self.openrouter_api_key}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": self.deepseek_model,
                "messages": [
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": "Hello"}
                ]
            }
            response = requests.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=10
            )
            if response.status_code == 200:
                return True
            else:
                print(f"DeepSeek (OpenRouter) error: {response.status_code} {response.text}")
                return False
        except Exception as e:
            print(f"Error initializing DeepSeek (OpenRouter): {e}")
            return False

    async def send_message(self, service: str, message: str, image_bytes: Optional[bytes] = None) -> Optional[str]:
        """Send message to selected AI service. If image_bytes is provided, send as vision/OCR."""
        try:
            def extract_ocr_text(image_bytes):
                img = Image.open(io.BytesIO(image_bytes))
                return pytesseract.image_to_string(img)

            if image_bytes:
                ocr_text = extract_ocr_text(image_bytes)
                prompt = f"{self.system_prompt}\n\nExtracted text from image:\n{ocr_text}\n\nUser question: {message}"
            else:
                prompt = message
            if service == "mistral" and self.mistral_client:
                try:
                    response = self.mistral_client.chat.complete(
                        model=self.mistral_model,
                        messages=[
                            {"role": "system", "content": self.system_prompt},
                            {"role": "user", "content": prompt}
                        ]
                    )
                    return response.choices[0].message.content
                except Exception as e:
                    print(f"Mistral error: {e}")
                    return None
            elif service == "gemini" and self.gemini_chat:
                try:
                    response = self.gemini_chat.send_message(prompt)
                    return response.text
                except Exception as e:
                    print(f"Gemini error: {e}")
                    return None
            elif service == "openai":
                try:
                    # Use OpenAI v1+ API
                    client = openai.OpenAI(api_key=openai.api_key)
                    response = client.chat.completions.create(
                        model="gpt-4.1",
                        messages=[
                            {"role": "system", "content": self.system_prompt},
                            {"role": "user", "content": prompt}
                        ]
                    )
                    return response.choices[0].message.content
                except Exception as e:
                    print(f"OpenAI error: {e}")
                    return None
            elif service == "deepseek" and hasattr(self, "openrouter_api_key"):
                headers = {
                    "Authorization": f"Bearer {self.openrouter_api_key}",
                    "Content-Type": "application/json"
                }
                messages = [
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": prompt}
                ]
                payload = {
                    "model": getattr(self, "deepseek_model", "deepseek/deepseek-r1-0528:free"),
                    "messages": messages
                }
                response = requests.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=60
                )
                if response.status_code == 200:
                    data = response.json()
                    return data["choices"][0]["message"]["content"]
                else:
                    print(f"DeepSeek (OpenRouter) error: {response.status_code} {response.text}")
                    return None
            else:
                print(f"Unknown or uninitialized service: {service}")
                return None
        except Exception as e:
            print(f"Error sending message: {e}")
            return None
