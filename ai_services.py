import google.generativeai as genai
import openai
import os
from typing import Dict, Any, Optional, List
import pytesseract
from PIL import Image
import io
import requests
import json

# Set the Tesseract executable path explicitly for reliability
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

class AIService:
    def __init__(self, mistral_model="mistral-small"):
        self.gemini_model = None
        self.gemini_chat = None
        self.mistral_client = None
        self.mistral_model = mistral_model
        self.ollama_base_url = "http://localhost:11434"
        self.ollama_available_models = []
        self.system_prompt = self.load_system_prompt()

    def load_system_prompt(self):
        try:
            with open(os.path.join(os.path.dirname(__file__), 'system_prompt.txt'), 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception:
            return "You are an AI assistant designed to help users with a wide range of tasks. "
    
    def initialize_gemini(self, api_key: str) -> bool:
        """Initialize Gemini API"""
        try:
            genai.configure(api_key=api_key)
            self.gemini_model = genai.GenerativeModel('gemini-2.0-flash')
            self.gemini_chat = self.gemini_model.start_chat(history=[])
            return True
        except Exception as e:
            print(f"❌ GEMINI INIT ERROR: {e}")
            return False

    def initialize_openai(self, api_key: str) -> bool:
        """Initialize OpenAI API"""
        try:
            openai.api_key = api_key
            return True
        except Exception as e:
            print(f"❌ OPENAI INIT ERROR: {e}")
            return False
        
    def initialize_mistral(self, api_key: str) -> bool:
        """Initialize Mistral API"""
        try:
            from mistralai import Mistral
            # Sanitize the API key to avoid illegal header errors
            api_key = api_key.strip().splitlines()[0]
            self.mistral_client = Mistral(api_key=api_key)
            # Test the connection with a simple request
            test_response = self.mistral_client.chat.complete(
                model=self.mistral_model,
                messages=[{"role": "user", "content": "test"}]
            )
            # If the test_response is a 401 error, raise a clear error
            if hasattr(test_response, 'status_code') and test_response.status_code == 401:
                raise Exception("Unauthorized: Your Mistral API key is invalid or expired.")
            return True if test_response else False
        except Exception as e:
            print(f"❌ MISTRAL INIT ERROR: {e}")
            return False

    def initialize_deepseek(self, api_key: str) -> bool:
        """Initialize DeepSeek (OpenRouter) API"""
        try:
            self.openrouter_api_key = api_key.strip()
            self.deepseek_model = "deepseek/deepseek-r1-0528:free"
            # Optionally, test the connection with a simple request
            headers = {
                "Authorization": f"Bearer {self.openrouter_api_key}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": self.deepseek_model,
                "messages": [
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": "Hello"}
                ]
            }
            response = requests.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=10
            )
            if response.status_code == 200:
                return True
            else:
                print(f"❌ DEEPSEEK INIT ERROR: {response.status_code} {response.text}")
                return False
        except Exception as e:
            print(f"❌ DEEPSEEK INIT ERROR: {e}")
            return False

    def get_ollama_models(self) -> List[str]:
        """Get list of available Ollama models"""
        try:
            print(f"🔍 CHECKING OLLAMA AT: {self.ollama_base_url}/api/tags")
            response = requests.get(f"{self.ollama_base_url}/api/tags")
            print(f"📡 OLLAMA RESPONSE STATUS: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                models = [model['name'] for model in data.get('models', [])]
                self.ollama_available_models = models
                print(f"📋 OLLAMA MODELS FOUND: {models}")
                return models
            else:
                print(f"❌ OLLAMA ERROR: {response.status_code} - {response.text}")
                return []
        except requests.exceptions.ConnectionError as e:
            print(f"❌ OLLAMA CONNECTION ERROR: Cannot connect to {self.ollama_base_url}")
            print(f"   Make sure Ollama is running with: ollama serve")
            return []
        except Exception as e:
            print(f"❌ OLLAMA UNEXPECTED ERROR: {e}")
            return []

    def initialize_ollama(self) -> bool:
        """Initialize Ollama connection"""
        try:
            print(f"🔍 INITIALIZING OLLAMA CONNECTION...")
            # Test connection by getting models
            models = self.get_ollama_models()
            if models:
                print(f"✅ OLLAMA CONNECTED: {len(models)} models available")
                print(f"📋 Available models: {', '.join(models)}")
                return True
            else:
                print("❌ OLLAMA INIT ERROR: No models found")
                print("   Try running: ollama pull llama2")
                return False
        except Exception as e:
            print(f"❌ OLLAMA INIT ERROR: {e}")
            return False

    async def send_message(self, service: str, message: str, image_bytes: Optional[bytes] = None) -> Optional[str]:
        """Send message to selected AI service. If image_bytes is provided, send as vision/OCR."""
        try:
            def extract_ocr_text(image_bytes):
                img = Image.open(io.BytesIO(image_bytes))
                return pytesseract.image_to_string(img)

            # Debug: Log the service being used
            print(f"\n{'='*50}")
            print(f"🤖 SERVICE: {service.upper()}")
            print(f"{'='*50}")

            if image_bytes:
                print("📷 OCR PROCESSING: Extracting text from image...")
                ocr_text = extract_ocr_text(image_bytes)
                print(f"\n📝 OCR RESULT:")
                print(f"{'─'*30}")
                print(f"{ocr_text}")
                print(f"{'─'*30}")
                prompt = f"{self.system_prompt}\n\nExtracted text from image:\n{ocr_text}\n\nUser question: {message}"
            else:
                prompt = message

            print(f"\n💬 USER MESSAGE: {message}")
            print(f"{'─'*50}")
            if service == "mistral" and self.mistral_client:
                try:
                    print(f"🔄 SENDING REQUEST TO MISTRAL ({self.mistral_model})...")
                    response = self.mistral_client.chat.complete(
                        model=self.mistral_model,
                        messages=[
                            {"role": "system", "content": self.system_prompt},
                            {"role": "user", "content": prompt}
                        ]
                    )
                    llm_answer = response.choices[0].message.content
                    print(f"\n🤖 LLM ANS (MISTRAL):")
                    print(f"{'─'*50}")
                    print(f"{llm_answer}")
                    print(f"{'─'*50}\n")
                    return llm_answer
                except Exception as e:
                    print(f"❌ MISTRAL ERROR: {e}")
                    return None
            elif service == "gemini" and self.gemini_chat:
                try:
                    print(f"🔄 SENDING REQUEST TO GEMINI...")
                    response = self.gemini_chat.send_message(prompt)
                    llm_answer = response.text
                    print(f"\n🤖 LLM ANS (GEMINI):")
                    print(f"{'─'*50}")
                    print(f"{llm_answer}")
                    print(f"{'─'*50}\n")
                    return llm_answer
                except Exception as e:
                    print(f"❌ GEMINI ERROR: {e}")
                    return None
            elif service == "openai":
                try:
                    print(f"🔄 SENDING REQUEST TO OPENAI (GPT-4.1)...")
                    # Use OpenAI v1+ API
                    client = openai.OpenAI(api_key=openai.api_key)
                    response = client.chat.completions.create(
                        model="gpt-4.1",
                        messages=[
                            {"role": "system", "content": self.system_prompt},
                            {"role": "user", "content": prompt}
                        ]
                    )
                    llm_answer = response.choices[0].message.content
                    print(f"\n🤖 LLM ANS (OPENAI):")
                    print(f"{'─'*50}")
                    print(f"{llm_answer}")
                    print(f"{'─'*50}\n")
                    return llm_answer
                except Exception as e:
                    print(f"❌ OPENAI ERROR: {e}")
                    return None
            elif service == "deepseek" and hasattr(self, "openrouter_api_key"):
                try:
                    model_name = getattr(self, "deepseek_model", "deepseek/deepseek-r1-0528:free")
                    print(f"🔄 SENDING REQUEST TO DEEPSEEK ({model_name})...")
                    headers = {
                        "Authorization": f"Bearer {self.openrouter_api_key}",
                        "Content-Type": "application/json"
                    }
                    messages = [
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": prompt}
                    ]
                    payload = {
                        "model": model_name,
                        "messages": messages
                    }
                    response = requests.post(
                        "https://openrouter.ai/api/v1/chat/completions",
                        headers=headers,
                        json=payload,
                        timeout=60
                    )
                    if response.status_code == 200:
                        data = response.json()
                        llm_answer = data["choices"][0]["message"]["content"]
                        print(f"\n🤖 LLM ANS (DEEPSEEK):")
                        print(f"{'─'*50}")
                        print(f"{llm_answer}")
                        print(f"{'─'*50}\n")
                        return llm_answer
                    else:
                        print(f"❌ DEEPSEEK ERROR: {response.status_code} {response.text}")
                        return None
                except Exception as e:
                    print(f"❌ DEEPSEEK ERROR: {e}")
                    return None
            elif service.startswith("ollama:"):
                try:
                    model_name = service.replace("ollama:", "")
                    print(f"🔄 SENDING REQUEST TO OLLAMA ({model_name})...")
                    print(f"📡 OLLAMA URL: {self.ollama_base_url}/api/generate")

                    payload = {
                        "model": model_name,
                        "prompt": f"System: {self.system_prompt}\n\nUser: {prompt}",
                        "stream": False
                    }

                    print(f"📤 OLLAMA PAYLOAD: {payload}")

                    # Remove timeout to allow longer processing
                    response = requests.post(
                        f"{self.ollama_base_url}/api/generate",
                        json=payload
                    )

                    print(f"📥 OLLAMA RESPONSE STATUS: {response.status_code}")

                    if response.status_code == 200:
                        data = response.json()
                        llm_answer = data.get("response", "")
                        print(f"\n🤖 LLM ANS (OLLAMA-{model_name.upper()}):")
                        print(f"{'─'*50}")
                        print(f"{llm_answer}")
                        print(f"{'─'*50}\n")
                        return llm_answer
                    else:
                        print(f"❌ OLLAMA ERROR: {response.status_code}")
                        print(f"❌ OLLAMA ERROR DETAILS: {response.text}")
                        return None
                except requests.exceptions.ConnectionError as e:
                    print(f"❌ OLLAMA CONNECTION ERROR: Cannot connect to {self.ollama_base_url}")
                    print(f"   Make sure Ollama is running with: ollama serve")
                    return None
                except Exception as e:
                    print(f"❌ OLLAMA UNEXPECTED ERROR: {e}")
                    return None
            else:
                print(f"❌ UNKNOWN OR UNINITIALIZED SERVICE: {service}")
                return None
        except Exception as e:
            print(f"❌ GENERAL ERROR: {e}")
            print(f"{'='*50}\n")
            return None
