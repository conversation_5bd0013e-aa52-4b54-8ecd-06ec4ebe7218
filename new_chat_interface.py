import tkinter as tk
from tkinter import scrolledtext, ttk, simpledialog, messagebox
import threading
import json
import os
from datetime import datetime

class ChatInterface(tk.Frame):
    def __init__(self, parent, toggle_theme_callback):
        self.toggle_theme_callback = toggle_theme_callback
        super().__init__(parent, bg='#2b2b2b')
        self.conversation_history = []
        self.conversation_id = datetime.now().strftime("%Y%m%d%H%M%S")
        self.thinking_position = None
        self.gemini_initialized = False
        
        # Initialize UI
        self.setup_ui()
        
    def setup_ui(self):
        # Chat display
        self.chat_display = scrolledtext.ScrolledText(
            self,
            bg='#2b2b2b',
            fg='#ffffff',
            font=("Segoe UI", 11),
            wrap=tk.WORD,
            relief=tk.FLAT,
            padx=10,
            pady=10,
            height=20,
            insertbackground='white'
        )
        self.chat_display.pack(fill=tk.BOTH, expand=True, padx=10, pady=(10, 5))
        self.chat_display.tag_configure("user", foreground="#4a90e2", font=("Segoe UI", 11, "bold"))
        self.chat_display.tag_configure("assistant", foreground="#ffffff", font=("Segoe UI", 11))
        self.chat_display.tag_configure("system", foreground="#888888", font=("Segoe UI", 10, "italic"))
        self.chat_display.config(state=tk.DISABLED)
        
        # Input frame
        self.input_frame = tk.Frame(self, bg='#2b2b2b')
        self.input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Theme toggle button
        self.theme_button = tk.Button(
            self.input_frame,
            text="🌓",
            command=self.toggle_theme_callback,
            font=("Segoe UI", 12),
            bg='#3b3b3b',
            fg='#ffffff',
            relief=tk.FLAT,
            padx=10
        )
        self.theme_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # Input field
        self.user_input = tk.Text(
            self.input_frame,
            height=2,
            bg='#3b3b3b',
            fg='#ffffff',
            font=("Segoe UI", 11),
            relief=tk.FLAT,
            padx=10,
            pady=3,
            wrap=tk.WORD,
            insertbackground='white'
        )
        self.user_input.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.user_input.bind("<Return>", self.on_enter_pressed)
        self.user_input.bind("<Shift-Return>", lambda e: None)  # Allow Shift+Enter for new lines
        
        # Send button
        self.send_button = tk.Button(
            self.input_frame,
            text="→",
            command=self.send_message,
            font=("Segoe UI", 12, "bold"),
            bg='#4a90e2',
            fg='#ffffff',
            relief=tk.FLAT,
            padx=15
        )
        self.send_button.pack(side=tk.LEFT, padx=(5, 0))
        
        # Set focus to input field
        self.user_input.focus_set()
    
    def on_enter_pressed(self, event):
        if event.state & 0x1:  # If Shift is pressed
            return  # Allow Shift+Enter for new lines
        self.send_message()
        return 'break'  # Prevent default Enter key behavior
    
    def send_message(self):
        user_message = self.user_input.get("1.0", tk.END).strip()
        if not user_message:
            return
            
        # Clear input and disable it while processing
        self.user_input.config(state=tk.DISABLED)
        self.user_input.delete("1.0", tk.END)
        
        # Add user message to chat
        self.add_message("user", user_message)
        
        # Show thinking message
        self.add_message("assistant", "Thinking...", is_thinking=True)
        
        # Re-enable input in case of errors
        def reenable_input():
            self.user_input.config(state=tk.NORMAL)
            self.user_input.focus_set()
        
        # Simulate AI response (replace with actual API call)
        def simulate_ai_response():
            import time
            time.sleep(1)  # Simulate processing time
            self.after(0, lambda: self.remove_thinking_message())
            self.after(0, lambda: self.add_message("assistant", "This is a test response. The actual implementation would connect to an AI API."))
            self.after(0, reenable_input)
        
        # Start response in a separate thread
        threading.Thread(target=simulate_ai_response, daemon=True).start()
    
    def add_message(self, sender, message, is_thinking=False):
        self.chat_display.config(state=tk.NORMAL)
        
        # Add sender tag
        if sender == "user":
            self.chat_display.insert(tk.END, "You: ", "user")
        elif sender == "assistant":
            if is_thinking:
                self.thinking_position = self.chat_display.index(tk.END)
                self.chat_display.insert(tk.END, "Assistant: ", "assistant")
                self.chat_display.insert(tk.END, message, "assistant")
                self.chat_display.see(tk.END)
                self.chat_display.config(state=tk.DISABLED)
                return
            else:
                self.chat_display.insert(tk.END, "Assistant: ", "assistant")
        
        # Add message
        self.chat_display.insert(tk.END, message + "\n\n")
        self.chat_display.see(tk.END)
        self.chat_display.config(state=tk.DISABLED)
    
    def remove_thinking_message(self):
        if self.thinking_position:
            self.chat_display.config(state=tk.NORMAL)
            self.chat_display.delete(self.thinking_position, f"{self.thinking_position} lineend +1l")
            self.chat_display.config(state=tk.DISABLED)
            self.thinking_position = None

# Test the chat interface
if __name__ == "__main__":
    root = tk.Tk()
    root.title("Chat Interface Test")
    root.geometry("500x600")
    
    def toggle_theme():
        print("Theme toggled")
    
    chat = ChatInterface(root, toggle_theme)
    chat.pack(fill=tk.BOTH, expand=True)
    
    root.mainloop()
