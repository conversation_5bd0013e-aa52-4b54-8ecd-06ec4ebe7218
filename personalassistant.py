import sys
import os
import json
import io
import threading
import ctypes
import pyautogui
import keyboard
import pytesseract
from PyQt5.QtWidgets import QApp<PERSON>, QWidget, QVBoxLayout, QLabel, QPushButton, QMessageBox
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QPainter, QPixmap, QImage
from ai_services import AIService

# Windows API constants
GWL_EXSTYLE = -20
WS_EX_TOOLWINDOW = 0x00000080
WS_EX_APPWINDOW = 0x00040000
WS_EX_LAYERED = 0x00080000
WS_EX_NOACTIVATE = 0x08000000

DEBUG_MODE = True
SYSTEM_PROMPT_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "system_prompt.txt")
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.json")

default_config = {
    'gemini_api_key': '',
    'mistral_api_key': '',
    'active_service': 'gemini',
    'mistral_model': 'mistral-small',
    'openrouter_api_key': ''
}

def load_config():
    config = default_config.copy()
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except Exception as e:
            print(f"Error loading config: {e}")
    return config

def save_config(config):
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)
    except Exception as e:
        print(f"Error saving config: {e}")

pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def load_system_prompt():
    # Updated system prompt for image interpretation and question answering
    return (
        "You are an AI assistant. Interpret the provided image and its OCR text. "
        "If there is a question present in the image, answer it directly. "
        "Otherwise, provide a helpful summary or interpretation."
        "If the image contains a multiple choice question, "
        "analyze the options and provide the answer first then the explanation. "
    )

class StealthScreenshotWindow(QWidget):
    result_signal = pyqtSignal(str)  # Correct: class attribute, not instance attribute
    def __init__(self):
        super().__init__()
        self.config = load_config()
        self.system_prompt = load_system_prompt()
        self._init_ai_service()
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_NoSystemBackground, True)
        self.setGeometry(300, 300, 800, 450)
        self._drag_pos = None
        self.screenshot_pixmap = None
        self.result_text = None
        self.scroll_offset = 0  # For scrolling through AI responses
        self.full_result_text = ""  # Store the complete AI response
        # Hello label
        self.hello_label = QLabel("Welcome to Lotus", self)
        self.hello_label.setStyleSheet("color:#fff;font-size:54pt;font-weight:bold;background:transparent;font-family:'Edwardian Script ITC', 'Segoe Script', cursive;")
        self.hello_label.setGeometry(60, 60, 700, 100)
        self.hello_label.show()
        # Model label (just below hello label)
        self.model_label = QLabel(self)
        self.model_label.setGeometry(60, 160, 600, 60)
        self.model_label.setStyleSheet("color:#aaa;font-size:28pt;font-weight:bold;background:transparent;")
        self.update_model_label()
        self.model_label.show()
        # Result label - will be dynamically sized
        self.result_label = QLabel(self)
        self.result_label.setStyleSheet("background:rgba(34,34,34,0.97);color:#fff;font-size:13pt;padding:18px;border-radius:10px;")
        self.result_label.setWordWrap(True)
        self.result_label.setGeometry(40, 40, 720, 370)  # Default size, will be adjusted
        self.result_label.hide()
        self.close_btn = QPushButton("✕", self)
        self.close_btn.setGeometry(720, 40, 40, 40)
        self.close_btn.setStyleSheet("background:transparent;color:#fff;font-size:18pt;border:none;")
        self.close_btn.clicked.connect(self.hide_result)
        self.close_btn.hide()
        self.apply_stealth_window_style()
        self.show()
        # Hotkey for settings
        keyboard.add_hotkey('alt+s', lambda: QTimer.singleShot(0, self.show_settings_dialog))
        # Connect result signal for thread-safe UI update
        self.result_signal.connect(self.show_result)
        # Global movement hotkeys (arrow keys and WASD, with/without Ctrl)
        self._register_movement_hotkeys()
        # Ctrl+\ to toggle window visibility (ensure it runs in Qt main thread)
        keyboard.add_hotkey('ctrl+\\', lambda: QTimer.singleShot(0, self.toggle_visibility))

    def toggle_visibility(self):
        print("[DEBUG] Toggling window visibility")
        try:
            if self.isVisible():
                self.hide_result()  # Hide result label if showing
                self.hide()  # Use hide() instead of setVisible(False)
                print("[DEBUG] Window hidden")
            else:
                self.show()  # Use show() instead of setVisible(True)
                self.raise_()
                self.activateWindow()
                self.update()
                print("[DEBUG] Window shown")
        except Exception as e:
            print(f"[ERROR] Toggle visibility failed: {e}")

    def _register_movement_hotkeys(self):
        # Unregister previous hotkeys to prevent memory leaks and duplicate callbacks
        try:
            for combo in [
                'left', 'right', 'up', 'down',
                'ctrl+left', 'ctrl+right', 'ctrl+up', 'ctrl+down',
                'a', 'd', 'w', 's',
                'ctrl+a', 'ctrl+d', 'ctrl+w', 'ctrl+s']:
                keyboard.remove_hotkey(combo)
        except Exception:
            pass
        try:
            # Movement controls: Ctrl+Left/Right arrows only
            keyboard.add_hotkey('ctrl+left', lambda: self._move_overlay(-20, 0))
            keyboard.add_hotkey('ctrl+right', lambda: self._move_overlay(20, 0))
            # Scrolling controls: Ctrl+Up/Down arrows for AI response scrolling
            keyboard.add_hotkey('ctrl+up', lambda: self._scroll_result(-1))
            keyboard.add_hotkey('ctrl+down', lambda: self._scroll_result(1))
        except Exception as e:
            print(f"[ERROR] Failed to register movement hotkeys: {e}")

    def _move_overlay(self, dx, dy):
        self.move(self.x() + dx, self.y() + dy)

    def _scroll_result(self, direction):
        """Scroll through AI response text. Direction: -1 for up, 1 for down"""
        if not self.result_label.isVisible() or not self.full_result_text:
            return

        # Calculate how many lines can fit in the result label
        font_metrics = self.result_label.fontMetrics()
        line_height = font_metrics.lineSpacing()
        available_height = self.result_label.height() - 36  # Account for padding
        lines_per_view = max(1, available_height // line_height)

        # Split text into lines for scrolling
        lines = self.full_result_text.split('\n')
        total_lines = len(lines)

        # Update scroll offset
        self.scroll_offset += direction * 3  # Scroll 3 lines at a time
        self.scroll_offset = max(0, min(self.scroll_offset, max(0, total_lines - lines_per_view)))

        # Update the displayed text
        self._update_displayed_text()

        print(f"[DEBUG] Scrolling: offset={self.scroll_offset}, total_lines={total_lines}, lines_per_view={lines_per_view}")

    def _update_displayed_text(self):
        """Update the displayed text in the result label based on scroll offset"""
        if not self.full_result_text:
            return

        # Calculate how many lines can fit in the result label
        font_metrics = self.result_label.fontMetrics()
        line_height = font_metrics.lineSpacing()
        available_height = self.result_label.height() - 36  # Account for padding
        lines_per_view = max(1, available_height // line_height)

        # Split text into lines for scrolling
        lines = self.full_result_text.split('\n')
        total_lines = len(lines)

        # Get the visible portion of text
        end_line = min(self.scroll_offset + lines_per_view, total_lines)
        visible_lines = lines[self.scroll_offset:end_line]
        visible_text = '\n'.join(visible_lines)

        # Add scroll indicators
        scroll_info = ""
        if self.scroll_offset > 0:
            scroll_info = "▲ Scroll up (Ctrl+↑) for more ▲\n\n"
        if end_line < total_lines:
            scroll_info += "\n\n▼ Scroll down (Ctrl+↓) for more ▼"

        display_text = scroll_info + visible_text if scroll_info else visible_text
        self.result_label.setText(display_text)

    def apply_stealth_window_style(self):
        hwnd = int(self.winId())
        ex_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        ex_style = (ex_style & ~WS_EX_APPWINDOW) | WS_EX_TOOLWINDOW | WS_EX_LAYERED | WS_EX_NOACTIVATE
        ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, ex_style)
        ctypes.windll.user32.ShowWindow(hwnd, 5)
        # Prevent focus stealing
        ctypes.windll.user32.SetWindowPos(hwnd, -1, 0, 0, 0, 0, 0x0001 | 0x0002 | 0x0010 | 0x0040)

    def event(self, e):
        # Prevent focus on click
        if e.type() == 176:  # QEvent.WindowActivate
            self.clearFocus()
            return False
        return super().event(e)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.SmoothPixmapTransform)
        if self.screenshot_pixmap:
            painter.drawPixmap(self.rect(), self.screenshot_pixmap)
        else:
            painter.fillRect(self.rect(), Qt.black)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self._drag_pos = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
        elif event.button() == Qt.RightButton:
            self.show_settings_dialog()

    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and self._drag_pos is not None:
            self.move(event.globalPos() - self._drag_pos)
            event.accept()

    def mouseReleaseEvent(self, event):
        self._drag_pos = None

    def keyPressEvent(self, event):
        if event.key() == Qt.Key_Escape:
            self.hide_result()
        # Remove movement logic from here

    def take_screenshot(self):
        print("[DEBUG] Taking screenshot...")
        # Use QApplication.primaryScreen for faster screenshot
        screen = QApplication.primaryScreen()
        qpix = screen.grabWindow(0)
        qimg = qpix.toImage().convertToFormat(QImage.Format_RGBA8888)
        width, height = qimg.width(), qimg.height()
        ptr = qimg.bits()
        ptr.setsize(qimg.byteCount())
        import numpy as np
        arr = np.array(ptr).reshape(height, width, 4)
        from PIL import Image
        img = Image.fromarray(arr, 'RGBA')
        self.screenshot_pixmap = QPixmap.fromImage(qimg).scaled(self.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.result_label.hide()
        self.close_btn.hide()
        self.update()
        print("[DEBUG] Screenshot taken, sending to LLM immediately...")
        self.send_screenshot_to_llm(img)

    def send_screenshot_to_llm(self, img):
        print("[DEBUG] Running OCR and LLM...")
        try:
            ocr_text = pytesseract.image_to_string(img)
        except Exception as e:
            print(f"[ERROR] OCR failed: {e}")
            error_text = f"Error: OCR failed - {str(e)}"
            self.result_signal.emit(error_text)
            return
        print(f"[DEBUG] OCR text: {ocr_text}")
        buf = io.BytesIO()
        img.save(buf, format='PNG')
        image_bytes = buf.getvalue()
        prompt = f"[OCR TEXT]\n{ocr_text.strip()}\n---\n{self.system_prompt}"
        try:
            import asyncio
            print("[DEBUG] LLM thread started")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            service = self.config.get('active_service', 'gemini')
            self._init_ai_service()
            print(f"[DEBUG] Sending to LLM service: {service}")
            result = loop.run_until_complete(
                self.ai_service.send_message(service, prompt, image_bytes=image_bytes)
            )
            loop.close()
            print(f"[DEBUG] LLM result: {result}")
            if not result:
                result = f"No response from {service}."
            self.result_signal.emit(result)
        except Exception as e:
            print(f"[ERROR] LLM error: {e}")
            error_text = f"Error: LLM request failed - {str(e)}"
            self.result_signal.emit(error_text)
        self.screenshot_pixmap = None

    @pyqtSlot(str)
    def show_result(self, text):
        # Store the full text for scrolling
        self.full_result_text = text
        self.scroll_offset = 0  # Reset scroll position

        # Calculate optimal size for the result label based on text length
        font_metrics = self.result_label.fontMetrics()
        line_height = font_metrics.lineSpacing()

        # Calculate text dimensions
        lines = text.split('\n')
        max_line_width = max([font_metrics.horizontalAdvance(line) for line in lines] + [400])  # Minimum width
        text_width = min(max_line_width + 40, 720)  # Max width with padding, cap at 720

        # Dynamic height calculation - show more lines for longer responses
        base_lines = 8  # Minimum lines to show
        max_lines = min(25, len(lines) + 2)  # Maximum lines, with some buffer
        display_lines = max(base_lines, min(max_lines, len(lines)))
        text_height = display_lines * line_height + 40  # Add padding

        # Ensure the result label fits within the window
        max_height = self.height() - 80  # Leave some margin
        text_height = min(text_height, max_height)

        # Update result label geometry
        x = (self.width() - text_width) // 2  # Center horizontally
        y = 40  # Fixed top position
        self.result_label.setGeometry(x, y, text_width, text_height)

        # Position close button relative to result label
        self.close_btn.setGeometry(x + text_width - 40, y, 40, 40)

        # Set initial text (may be truncated if too long)
        self._update_displayed_text()

        self.result_label.setParent(self)  # Ensure parent is main window
        self.result_label.show()
        self.result_label.raise_()         # Bring label to front
        self.close_btn.show()
        self.close_btn.raise_()
        self.screenshot_pixmap = None
        self.show()
        self.raise_()
        self.activateWindow()
        self.update()
        self.repaint()
        self.result_label.repaint()
        print(f"[DEBUG] result_label geometry: {self.result_label.geometry()} visible: {self.result_label.isVisible()}")
        print(f"[DEBUG] Text lines: {len(lines)}, Display lines: {display_lines}, Size: {text_width}x{text_height}")
        self.result_label.setFocus()
        QTimer.singleShot(200, lambda: self._ensure_result_visible(text))
        QTimer.singleShot(60000, self.hide_result)  # Show for 60 seconds instead of 12

    def _ensure_result_visible(self, text):
        # Always show in the stealth window, never in a popup
        self.result_label.setText(text)
        self.result_label.show()
        self.result_label.raise_()
        self.close_btn.show()
        self.close_btn.raise_()
        self.show()
        self.raise_()
        self.update()

    def hide_result(self):
        self.result_label.hide()
        self.close_btn.hide()
        self.screenshot_pixmap = None
        self.update()

    def update_model_label(self):
        service = self.config.get('active_service', 'gemini')
        model = ''
        if service == 'gemini':
            model = 'Gemini'
        elif service == 'mistral':
            model = self.config.get('mistral_model', 'mistral-small')
        elif service == 'deepseek':
            model = 'DeepSeek (OpenRouter)'
        else:
            model = service
        self.model_label.setText(f"Active Service: {model}")

    def show_settings_dialog(self):
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton, QComboBox
        class SettingsDialog(QDialog):
            def __init__(self, config, parent=None):
                super().__init__(parent)
                self.setWindowTitle("Settings")
                self.setModal(True)
                self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool)
                layout = QVBoxLayout()
                # Service selection
                layout.addWidget(QLabel("Active Service:"))
                self.service_combo = QComboBox()
                self.service_combo.addItems(["gemini", "mistral", "deepseek"])
                self.service_combo.setCurrentText(config.get('active_service', 'gemini'))
                layout.addWidget(self.service_combo)
                # API key fields
                self.gemini_entry = QLineEdit()
                self.gemini_entry.setPlaceholderText("Gemini API Key")
                self.gemini_entry.setText(config.get('gemini_api_key', ''))
                layout.addWidget(self.gemini_entry)
                self.mistral_entry = QLineEdit()
                self.mistral_entry.setPlaceholderText("Mistral API Key")
                self.mistral_entry.setText(config.get('mistral_api_key', ''))
                layout.addWidget(self.mistral_entry)
                self.deepseek_entry = QLineEdit()
                self.deepseek_entry.setPlaceholderText("OpenRouter API Key (DeepSeek)")
                self.deepseek_entry.setText(config.get('openrouter_api_key', ''))
                layout.addWidget(self.deepseek_entry)
                save_btn = QPushButton("Save")
                save_btn.clicked.connect(self.accept)
                layout.addWidget(save_btn)
                self.setLayout(layout)
            def get_config(self):
                return {
                    'active_service': self.service_combo.currentText(),
                    'gemini_api_key': self.gemini_entry.text().strip(),
                    'mistral_api_key': self.mistral_entry.text().strip(),
                    'openrouter_api_key': self.deepseek_entry.text().strip(),
                    'mistral_model': self.parent().config.get('mistral_model', 'mistral-small')
                }
        dlg = SettingsDialog(self.config, self)
        if dlg.exec_() == QDialog.Accepted:
            self.config.update(dlg.get_config())
            save_config(self.config)
            self._init_ai_service()
            self.update_model_label()

    def _init_ai_service(self):
        # Always initialize the correct service
        self.ai_service = AIService(mistral_model=self.config.get('mistral_model', 'mistral-small'))
        service = self.config.get('active_service', 'gemini')
        if service == 'gemini' and self.config.get('gemini_api_key'):
            self.ai_service.initialize_gemini(self.config['gemini_api_key'])
        elif service == 'mistral' and self.config.get('mistral_api_key'):
            self.ai_service.initialize_mistral(self.config['mistral_api_key'])
        elif service == 'deepseek' and self.config.get('openrouter_api_key'):
            self.ai_service.initialize_deepseek(self.config['openrouter_api_key'])

def prompt_for_api_key_if_missing(config):
    from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton
    service = config.get('active_service', 'gemini')
    key_map = {
        'gemini': ('gemini_api_key', 'Gemini API Key'),
        'mistral': ('mistral_api_key', 'Mistral API Key'),
        'deepseek': ('openrouter_api_key', 'OpenRouter API Key (DeepSeek)')
    }
    key_name, label = key_map.get(service, (None, None))
    if not key_name:
        return config
    if config.get(key_name):
        return config
    # Prompt user for API key
    class ApiKeyDialog(QDialog):
        def __init__(self, label, parent=None):
            super().__init__(parent)
            self.setWindowTitle("API Key Required")
            self.setModal(True)
            layout = QVBoxLayout()
            layout.addWidget(QLabel(f"Enter your {label}:"))
            self.entry = QLineEdit()
            self.entry.setEchoMode(QLineEdit.Password)
            layout.addWidget(self.entry)
            btn = QPushButton("Save")
            btn.clicked.connect(self.accept)
            layout.addWidget(btn)
            self.setLayout(layout)
        def get_key(self):
            return self.entry.text().strip()
    dlg = ApiKeyDialog(label)
    if dlg.exec_() == QDialog.Accepted:
        config[key_name] = dlg.get_key()
        save_config(config)
    return config

def start_app():
    app = QApplication(sys.argv)
    config = load_config()
    config = prompt_for_api_key_if_missing(config)
    window = StealthScreenshotWindow()
    window.config = config
    window.ai_service = AIService(mistral_model=config['mistral_model'])
    window.show()  # Ensure window is shown
    def on_hotkey():
        QTimer.singleShot(0, window.take_screenshot)
    # Unregister before registering to avoid duplicate hotkeys
    try:
        keyboard.remove_hotkey('alt+c')
    except Exception:
        pass
    keyboard.add_hotkey('alt+c', on_hotkey)
    sys.exit(app.exec_())

# Add a global exception hook to log uncaught exceptions and show a user-friendly error message
import traceback
from PyQt5.QtWidgets import QMessageBox

def exception_hook(exctype, value, tb):
    error_msg = ''.join(traceback.format_exception(exctype, value, tb))
    print(f"[CRITICAL] Uncaught exception:\n{error_msg}")
    try:
        QMessageBox.critical(None, "Application Error", f"A critical error occurred:\n{value}\n\nSee console for details.")
    except Exception:
        pass
    sys.__excepthook__(exctype, value, tb)

sys.excepthook = exception_hook

if __name__ == '__main__':
    start_app()