import sys
import os
import json
import io
import threading
import ctypes
import pyautogui
import keyboard
import pytesseract
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QMessageBox, QComboBox, QLineEdit, QDialog,
                             QScrollArea, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QPainter, QPixmap, QImage, QFont
from ai_services import AIService

# Windows API constants
GWL_EXSTYLE = -20
WS_EX_TOOLWINDOW = 0x00000080
WS_EX_APPWINDOW = 0x00040000
WS_EX_LAYERED = 0x00080000
WS_EX_NOACTIVATE = 0x08000000

DEBUG_MODE = True
SYSTEM_PROMPT_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "system_prompt.txt")
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.json")

default_config = {
    'gemini_api_key': '',
    'mistral_api_key': '',
    'active_service': 'gemini',
    'mistral_model': 'mistral-small',
    'openrouter_api_key': ''
}

def load_config():
    config = default_config.copy()
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except Exception as e:
            print(f"Error loading config: {e}")
    return config

def save_config(config):
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)
    except Exception as e:
        print(f"Error saving config: {e}")

pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def load_system_prompt():
    # Updated system prompt for image interpretation and question answering
    return (
        "You are an AI assistant. Interpret the provided image and its OCR text. "
        "If there is a question present in the image, answer it directly. "
        "Otherwise, provide a helpful summary or interpretation."
        "If the image contains a multiple choice question, "
        "analyze the options and provide the answer first then the explanation. "
    )

class TaskbarControlPanel(QWidget):
    """Transparent taskbar-style control panel"""
    model_changed = pyqtSignal(str)

    def __init__(self, config):
        super().__init__()
        self.config = config
        self.init_ui()
        self.apply_stealth_window_style()

    def init_ui(self):
        # Set window properties for taskbar-style appearance
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_NoSystemBackground, True)

        # Position at top of screen, full width but thin height
        screen = QApplication.primaryScreen().geometry()
        self.setGeometry(0, 0, screen.width(), 60)

        # Create main layout
        layout = QHBoxLayout()
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(15)

        # App title/logo
        title_label = QLabel("🔍 Lotus AI")
        title_label.setStyleSheet("""
            color: #fff;
            font-size: 18pt;
            font-weight: bold;
            background: rgba(0,0,0,0.8);
            padding: 8px 15px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
        """)
        layout.addWidget(title_label)

        # Model selector
        model_label = QLabel("Model:")
        model_label.setStyleSheet("color: #fff; font-size: 12pt; background: transparent;")
        layout.addWidget(model_label)

        self.model_combo = QComboBox()
        self.model_combo.addItems(["Gemini", "Mistral", "DeepSeek"])
        self.model_combo.setCurrentText(self._get_display_model())
        self.model_combo.setStyleSheet("""
            QComboBox {
                background: rgba(0,0,0,0.8);
                color: #fff;
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 6px;
                padding: 5px 10px;
                font-size: 11pt;
                min-width: 100px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #fff;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background: rgba(0,0,0,0.9);
                color: #fff;
                border: 1px solid rgba(255,255,255,0.2);
                selection-background-color: rgba(255,255,255,0.2);
            }
        """)
        self.model_combo.currentTextChanged.connect(self._on_model_changed)
        layout.addWidget(self.model_combo)

        # Status indicator
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("""
            color: #4CAF50;
            font-size: 11pt;
            background: rgba(0,0,0,0.8);
            padding: 5px 10px;
            border-radius: 6px;
            border: 1px solid rgba(76,175,80,0.3);
        """)
        layout.addWidget(self.status_label)

        # Spacer to push controls to the right
        layout.addStretch()

        # Hotkey info
        hotkey_label = QLabel("Alt+C: Screenshot | Alt+S: Settings | Ctrl+\\: Toggle")
        hotkey_label.setStyleSheet("""
            color: #aaa;
            font-size: 10pt;
            background: rgba(0,0,0,0.6);
            padding: 5px 10px;
            border-radius: 6px;
        """)
        layout.addWidget(hotkey_label)

        # Settings button
        settings_btn = QPushButton("⚙️")
        settings_btn.setStyleSheet("""
            QPushButton {
                background: rgba(0,0,0,0.8);
                color: #fff;
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14pt;
            }
            QPushButton:hover {
                background: rgba(255,255,255,0.1);
            }
        """)
        settings_btn.clicked.connect(self._show_settings)
        layout.addWidget(settings_btn)

        self.setLayout(layout)

    def _get_display_model(self):
        service = self.config.get('active_service', 'gemini')
        if service == 'gemini':
            return 'Gemini'
        elif service == 'mistral':
            return 'Mistral'
        elif service == 'deepseek':
            return 'DeepSeek'
        return 'Gemini'

    def _on_model_changed(self, model_text):
        service_map = {
            'Gemini': 'gemini',
            'Mistral': 'mistral',
            'DeepSeek': 'deepseek'
        }
        service = service_map.get(model_text, 'gemini')
        self.config['active_service'] = service
        save_config(self.config)
        self.model_changed.emit(service)

    def _show_settings(self):
        # This will be connected to the main window's settings dialog
        pass

    def update_status(self, status, color="#4CAF50"):
        self.status_label.setText(status)
        self.status_label.setStyleSheet(f"""
            color: {color};
            font-size: 11pt;
            background: rgba(0,0,0,0.8);
            padding: 5px 10px;
            border-radius: 6px;
            border: 1px solid {color}33;
        """)

    def apply_stealth_window_style(self):
        hwnd = int(self.winId())
        ex_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        ex_style = (ex_style & ~WS_EX_APPWINDOW) | WS_EX_TOOLWINDOW | WS_EX_LAYERED | WS_EX_NOACTIVATE
        ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, ex_style)
        ctypes.windll.user32.ShowWindow(hwnd, 5)
        # Prevent focus stealing
        ctypes.windll.user32.SetWindowPos(hwnd, -1, 0, 0, 0, 0, 0x0001 | 0x0002 | 0x0010 | 0x0040)

class AIResponseWindow(QWidget):
    """Separate popup window for AI responses with dynamic sizing"""
    def __init__(self):
        super().__init__()
        self.full_result_text = ""
        self.scroll_offset = 0
        self.init_ui()
        self.apply_stealth_window_style()

    def init_ui(self):
        # Set window properties
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_NoSystemBackground, True)

        # Create main layout
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        # Result display area
        self.result_label = QLabel()
        self.result_label.setStyleSheet("""
            background: rgba(34,34,34,0.95);
            color: #fff;
            font-size: 13pt;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(255,255,255,0.1);
        """)
        self.result_label.setWordWrap(True)
        layout.addWidget(self.result_label)

        # Close button
        self.close_btn = QPushButton("✕")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255,0,0,0.8);
                color: #fff;
                border: none;
                border-radius: 15px;
                font-size: 16pt;
                font-weight: bold;
                width: 30px;
                height: 30px;
            }
            QPushButton:hover {
                background: rgba(255,0,0,1.0);
            }
        """)
        self.close_btn.setFixedSize(30, 30)
        self.close_btn.clicked.connect(self.hide)

        # Position close button in top-right corner
        self.close_btn.setParent(self)

        self.setLayout(layout)

    def show_result(self, text):
        """Display AI response with dynamic sizing"""
        self.full_result_text = text
        self.scroll_offset = 0

        # Calculate optimal window size
        font_metrics = self.result_label.fontMetrics()
        lines = text.split('\n')

        # Calculate width based on longest line
        max_line_width = max([font_metrics.horizontalAdvance(line) for line in lines] + [400])
        window_width = min(max_line_width + 60, 800)  # Max width 800px

        # Calculate height based on number of lines
        line_height = font_metrics.lineSpacing()
        max_display_lines = min(30, len(lines) + 2)  # Max 30 lines
        window_height = min(max_display_lines * line_height + 60, 600)  # Max height 600px

        # Center the window on screen
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - window_width) // 2
        y = (screen.height() - window_height) // 2

        self.setGeometry(x, y, window_width, window_height)

        # Position close button
        self.close_btn.move(window_width - 40, 10)

        # Update displayed text
        self._update_displayed_text()

        # Show the window
        self.show()
        self.raise_()
        self.activateWindow()

        # Auto-hide after 60 seconds
        QTimer.singleShot(60000, self.hide)

    def _update_displayed_text(self):
        """Update the displayed text based on scroll offset"""
        if not self.full_result_text:
            return

        font_metrics = self.result_label.fontMetrics()
        line_height = font_metrics.lineSpacing()
        available_height = self.height() - 80  # Account for padding
        lines_per_view = max(1, available_height // line_height)

        lines = self.full_result_text.split('\n')
        total_lines = len(lines)

        # Get visible portion
        end_line = min(self.scroll_offset + lines_per_view, total_lines)
        visible_lines = lines[self.scroll_offset:end_line]
        visible_text = '\n'.join(visible_lines)

        # Add scroll indicators
        scroll_info = ""
        if self.scroll_offset > 0:
            scroll_info = "▲ Scroll up (Ctrl+↑) for more ▲\n\n"
        if end_line < total_lines:
            scroll_info += "\n\n▼ Scroll down (Ctrl+↓) for more ▼"

        display_text = scroll_info + visible_text if scroll_info else visible_text
        self.result_label.setText(display_text)

    def scroll_result(self, direction):
        """Scroll through the AI response"""
        if not self.full_result_text or not self.isVisible():
            return

        font_metrics = self.result_label.fontMetrics()
        line_height = font_metrics.lineSpacing()
        available_height = self.height() - 80
        lines_per_view = max(1, available_height // line_height)

        lines = self.full_result_text.split('\n')
        total_lines = len(lines)

        # Update scroll offset
        self.scroll_offset += direction * 3  # Scroll 3 lines at a time
        self.scroll_offset = max(0, min(self.scroll_offset, max(0, total_lines - lines_per_view)))

        # Update display
        self._update_displayed_text()

    def apply_stealth_window_style(self):
        hwnd = int(self.winId())
        ex_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        ex_style = (ex_style & ~WS_EX_APPWINDOW) | WS_EX_TOOLWINDOW | WS_EX_LAYERED | WS_EX_NOACTIVATE
        ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, ex_style)
        ctypes.windll.user32.ShowWindow(hwnd, 5)
        # Prevent focus stealing
        ctypes.windll.user32.SetWindowPos(hwnd, -1, 0, 0, 0, 0, 0x0001 | 0x0002 | 0x0010 | 0x0040)

class StealthScreenshotWindow(QWidget):
    """Main invisible window that handles screenshots and coordinates other windows"""
    result_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.config = load_config()
        self.system_prompt = load_system_prompt()
        self._init_ai_service()

        # Make this window completely invisible - it's just for coordination
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_NoSystemBackground, True)
        self.setGeometry(-1000, -1000, 1, 1)  # Move off-screen

        self.screenshot_pixmap = None

        # Create the taskbar control panel
        self.control_panel = TaskbarControlPanel(self.config)
        self.control_panel.model_changed.connect(self._on_model_changed)
        self.control_panel.show()

        # Create the AI response window (initially hidden)
        self.response_window = AIResponseWindow()

        # Connect settings button in control panel
        self.control_panel._show_settings = self.show_settings_dialog

        # Connect result signal for thread-safe UI update
        self.result_signal.connect(self._show_ai_response)

        # Register hotkeys
        self._register_hotkeys()

        self.apply_stealth_window_style()

    def _on_model_changed(self, service):
        """Handle model change from control panel"""
        self.config['active_service'] = service
        self._init_ai_service()
        self.control_panel.update_status(f"Switched to {service.title()}", "#4CAF50")
        QTimer.singleShot(2000, lambda: self.control_panel.update_status("Ready", "#4CAF50"))

    def _register_hotkeys(self):
        """Register global hotkeys"""
        # Screenshot hotkey
        keyboard.add_hotkey('alt+c', lambda: QTimer.singleShot(0, self.take_screenshot))

        # Settings hotkey
        keyboard.add_hotkey('alt+s', lambda: QTimer.singleShot(0, self.show_settings_dialog))

        # Toggle control panel visibility
        keyboard.add_hotkey('ctrl+\\', lambda: QTimer.singleShot(0, self.toggle_control_panel))

        # Scrolling controls for AI response window
        keyboard.add_hotkey('ctrl+up', lambda: self.response_window.scroll_result(-1))
        keyboard.add_hotkey('ctrl+down', lambda: self.response_window.scroll_result(1))

        # Movement controls for control panel
        keyboard.add_hotkey('ctrl+left', lambda: self._move_control_panel(-20, 0))
        keyboard.add_hotkey('ctrl+right', lambda: self._move_control_panel(20, 0))

    def _move_control_panel(self, dx, dy):
        """Move the control panel"""
        self.control_panel.move(self.control_panel.x() + dx, self.control_panel.y() + dy)

    def toggle_control_panel(self):
        """Toggle control panel visibility"""
        if self.control_panel.isVisible():
            self.control_panel.hide()
            self.response_window.hide()
        else:
            self.control_panel.show()
            self.control_panel.raise_()
            self.control_panel.activateWindow()



    def apply_stealth_window_style(self):
        hwnd = int(self.winId())
        ex_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        ex_style = (ex_style & ~WS_EX_APPWINDOW) | WS_EX_TOOLWINDOW | WS_EX_LAYERED | WS_EX_NOACTIVATE
        ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, ex_style)
        ctypes.windll.user32.ShowWindow(hwnd, 5)
        # Prevent focus stealing
        ctypes.windll.user32.SetWindowPos(hwnd, -1, 0, 0, 0, 0, 0x0001 | 0x0002 | 0x0010 | 0x0040)

    def take_screenshot(self):
        print("[DEBUG] Taking screenshot...")
        self.control_panel.update_status("Taking screenshot...", "#FF9800")

        # Use QApplication.primaryScreen for faster screenshot
        screen = QApplication.primaryScreen()
        qpix = screen.grabWindow(0)
        qimg = qpix.toImage().convertToFormat(QImage.Format_RGBA8888)
        width, height = qimg.width(), qimg.height()
        ptr = qimg.bits()
        ptr.setsize(qimg.byteCount())
        import numpy as np
        arr = np.array(ptr).reshape(height, width, 4)
        from PIL import Image
        img = Image.fromarray(arr, 'RGBA')

        print("[DEBUG] Screenshot taken, sending to LLM immediately...")
        self.control_panel.update_status("Processing with AI...", "#2196F3")
        self.send_screenshot_to_llm(img)

    def send_screenshot_to_llm(self, img):
        print("[DEBUG] Running OCR and LLM...")
        try:
            ocr_text = pytesseract.image_to_string(img)
        except Exception as e:
            print(f"[ERROR] OCR failed: {e}")
            error_text = f"Error: OCR failed - {str(e)}"
            self.control_panel.update_status("OCR failed", "#F44336")
            self.result_signal.emit(error_text)
            return
        print(f"[DEBUG] OCR text: {ocr_text}")
        buf = io.BytesIO()
        img.save(buf, format='PNG')
        image_bytes = buf.getvalue()
        prompt = f"[OCR TEXT]\n{ocr_text.strip()}\n---\n{self.system_prompt}"
        try:
            import asyncio
            print("[DEBUG] LLM thread started")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            service = self.config.get('active_service', 'gemini')
            self._init_ai_service()
            print(f"[DEBUG] Sending to LLM service: {service}")
            result = loop.run_until_complete(
                self.ai_service.send_message(service, prompt, image_bytes=image_bytes)
            )
            loop.close()
            print(f"[DEBUG] LLM result: {result}")
            if not result:
                result = f"No response from {service}."
                self.control_panel.update_status("No AI response", "#FF9800")
            else:
                self.control_panel.update_status("Response ready", "#4CAF50")
            self.result_signal.emit(result)
        except Exception as e:
            print(f"[ERROR] LLM error: {e}")
            error_text = f"Error: LLM request failed - {str(e)}"
            self.control_panel.update_status("AI request failed", "#F44336")
            self.result_signal.emit(error_text)

    @pyqtSlot(str)
    def _show_ai_response(self, text):
        """Show AI response in the popup window"""
        self.response_window.show_result(text)
        # Reset status after showing result
        QTimer.singleShot(3000, lambda: self.control_panel.update_status("Ready", "#4CAF50"))

    def show_settings_dialog(self):
        class SettingsDialog(QDialog):
            def __init__(self, config, parent=None):
                super().__init__(parent)
                self.setWindowTitle("Lotus AI Settings")
                self.setModal(True)
                self.setWindowFlags(Qt.WindowStaysOnTopHint)
                self.setStyleSheet("""
                    QDialog {
                        background: rgba(34,34,34,0.95);
                        color: #fff;
                        border-radius: 10px;
                    }
                    QLabel {
                        color: #fff;
                        font-size: 12pt;
                        padding: 5px;
                    }
                    QLineEdit {
                        background: rgba(0,0,0,0.8);
                        color: #fff;
                        border: 1px solid rgba(255,255,255,0.2);
                        border-radius: 6px;
                        padding: 8px;
                        font-size: 11pt;
                    }
                    QComboBox {
                        background: rgba(0,0,0,0.8);
                        color: #fff;
                        border: 1px solid rgba(255,255,255,0.2);
                        border-radius: 6px;
                        padding: 8px;
                        font-size: 11pt;
                    }
                    QPushButton {
                        background: rgba(76,175,80,0.8);
                        color: #fff;
                        border: none;
                        border-radius: 6px;
                        padding: 10px 20px;
                        font-size: 12pt;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background: rgba(76,175,80,1.0);
                    }
                """)
                layout = QVBoxLayout()
                layout.setSpacing(15)
                layout.setContentsMargins(20, 20, 20, 20)

                # Title
                title = QLabel("🔍 Lotus AI Settings")
                title.setStyleSheet("font-size: 16pt; font-weight: bold; color: #4CAF50; padding: 10px;")
                layout.addWidget(title)

                # Service selection
                layout.addWidget(QLabel("Active AI Service:"))
                self.service_combo = QComboBox()
                self.service_combo.addItems(["gemini", "mistral", "deepseek"])
                self.service_combo.setCurrentText(config.get('active_service', 'gemini'))
                layout.addWidget(self.service_combo)

                # API key fields
                layout.addWidget(QLabel("Gemini API Key:"))
                self.gemini_entry = QLineEdit()
                self.gemini_entry.setPlaceholderText("Enter your Gemini API key...")
                self.gemini_entry.setText(config.get('gemini_api_key', ''))
                self.gemini_entry.setEchoMode(QLineEdit.Password)
                layout.addWidget(self.gemini_entry)

                layout.addWidget(QLabel("Mistral API Key:"))
                self.mistral_entry = QLineEdit()
                self.mistral_entry.setPlaceholderText("Enter your Mistral API key...")
                self.mistral_entry.setText(config.get('mistral_api_key', ''))
                self.mistral_entry.setEchoMode(QLineEdit.Password)
                layout.addWidget(self.mistral_entry)

                layout.addWidget(QLabel("OpenRouter API Key (for DeepSeek):"))
                self.deepseek_entry = QLineEdit()
                self.deepseek_entry.setPlaceholderText("Enter your OpenRouter API key...")
                self.deepseek_entry.setText(config.get('openrouter_api_key', ''))
                self.deepseek_entry.setEchoMode(QLineEdit.Password)
                layout.addWidget(self.deepseek_entry)

                # Save button
                save_btn = QPushButton("💾 Save Settings")
                save_btn.clicked.connect(self.accept)
                layout.addWidget(save_btn)

                self.setLayout(layout)
                self.resize(400, 500)

            def get_config(self):
                return {
                    'active_service': self.service_combo.currentText(),
                    'gemini_api_key': self.gemini_entry.text().strip(),
                    'mistral_api_key': self.mistral_entry.text().strip(),
                    'openrouter_api_key': self.deepseek_entry.text().strip(),
                    'mistral_model': 'mistral-small'  # Keep default
                }

        dlg = SettingsDialog(self.config, self.control_panel)
        if dlg.exec_() == QDialog.Accepted:
            old_service = self.config.get('active_service', 'gemini')
            self.config.update(dlg.get_config())
            save_config(self.config)
            self._init_ai_service()

            # Update control panel
            new_service = self.config.get('active_service', 'gemini')
            service_display = {'gemini': 'Gemini', 'mistral': 'Mistral', 'deepseek': 'DeepSeek'}
            self.control_panel.model_combo.setCurrentText(service_display.get(new_service, 'Gemini'))

            if old_service != new_service:
                self.control_panel.update_status(f"Switched to {new_service.title()}", "#4CAF50")
                QTimer.singleShot(3000, lambda: self.control_panel.update_status("Ready", "#4CAF50"))
            else:
                self.control_panel.update_status("Settings saved", "#4CAF50")
                QTimer.singleShot(2000, lambda: self.control_panel.update_status("Ready", "#4CAF50"))

    def _init_ai_service(self):
        # Always initialize the correct service
        self.ai_service = AIService(mistral_model=self.config.get('mistral_model', 'mistral-small'))
        service = self.config.get('active_service', 'gemini')
        if service == 'gemini' and self.config.get('gemini_api_key'):
            self.ai_service.initialize_gemini(self.config['gemini_api_key'])
        elif service == 'mistral' and self.config.get('mistral_api_key'):
            self.ai_service.initialize_mistral(self.config['mistral_api_key'])
        elif service == 'deepseek' and self.config.get('openrouter_api_key'):
            self.ai_service.initialize_deepseek(self.config['openrouter_api_key'])

def prompt_for_api_key_if_missing(config):
    from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton
    service = config.get('active_service', 'gemini')
    key_map = {
        'gemini': ('gemini_api_key', 'Gemini API Key'),
        'mistral': ('mistral_api_key', 'Mistral API Key'),
        'deepseek': ('openrouter_api_key', 'OpenRouter API Key (DeepSeek)')
    }
    key_name, label = key_map.get(service, (None, None))
    if not key_name:
        return config
    if config.get(key_name):
        return config
    # Prompt user for API key
    class ApiKeyDialog(QDialog):
        def __init__(self, label, parent=None):
            super().__init__(parent)
            self.setWindowTitle("API Key Required")
            self.setModal(True)
            layout = QVBoxLayout()
            layout.addWidget(QLabel(f"Enter your {label}:"))
            self.entry = QLineEdit()
            self.entry.setEchoMode(QLineEdit.Password)
            layout.addWidget(self.entry)
            btn = QPushButton("Save")
            btn.clicked.connect(self.accept)
            layout.addWidget(btn)
            self.setLayout(layout)
        def get_key(self):
            return self.entry.text().strip()
    dlg = ApiKeyDialog(label)
    if dlg.exec_() == QDialog.Accepted:
        config[key_name] = dlg.get_key()
        save_config(config)
    return config

def start_app():
    app = QApplication(sys.argv)
    config = load_config()
    config = prompt_for_api_key_if_missing(config)
    window = StealthScreenshotWindow()
    window.config = config
    window.ai_service = AIService(mistral_model=config['mistral_model'])
    window.show()  # Ensure window is shown
    def on_hotkey():
        QTimer.singleShot(0, window.take_screenshot)
    # Unregister before registering to avoid duplicate hotkeys
    try:
        keyboard.remove_hotkey('alt+c')
    except Exception:
        pass
    keyboard.add_hotkey('alt+c', on_hotkey)
    sys.exit(app.exec_())

# Add a global exception hook to log uncaught exceptions and show a user-friendly error message
import traceback
from PyQt5.QtWidgets import QMessageBox

def exception_hook(exctype, value, tb):
    error_msg = ''.join(traceback.format_exception(exctype, value, tb))
    print(f"[CRITICAL] Uncaught exception:\n{error_msg}")
    try:
        QMessageBox.critical(None, "Application Error", f"A critical error occurred:\n{value}\n\nSee console for details.")
    except Exception:
        pass
    sys.__excepthook__(exctype, value, tb)

sys.excepthook = exception_hook

if __name__ == '__main__':
    start_app()