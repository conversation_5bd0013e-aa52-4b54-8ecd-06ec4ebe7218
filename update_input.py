import re

# Read the original file
with open('personalassistant.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Update the input field height and padding
content = re.sub(
    r'(self\.user_input = tk\.Text\([\s\S]*?height=)3([\s\S]*?pady=)5([\s\S]*?\),)',
    r'\g<1>2\g<2>3\g<3>',  # Change height from 3 to 2 and pady from 5 to 3
    content
)

# Write the updated content back to the file
with open('personalassistant.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("Updated chat input height and padding.")
